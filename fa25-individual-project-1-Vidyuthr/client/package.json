{"name": "@fake-stack-overflow/client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview"}, "dependencies": {"@fake-stack-overflow/shared": "^1.0.0", "axios": "^1.9.0", "mongodb": "^6.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.3", "socket.io-client": "^4.8.1", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.24.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/parser": "^8.29.0", "@vitejs/plugin-react": "^4.3.4", "dotenv": "^16.4.7", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "^5.7.2", "typescript-eslint": "^8.29.0", "vite": "^6.2.0"}}