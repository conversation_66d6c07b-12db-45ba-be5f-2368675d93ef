/* Container styles */
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f7f7f7;
  padding: 20px;
}

/* Heading styles */
.container h2 {
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
}

.container h3 {
  font-size: 1.5rem;
  color: #555;
  margin-bottom: 20px;
}

.container h4 {
  font-size: 1rem;
  color: #666;
  margin: 15px 0 5px; /* Adjusted for better spacing */
  text-align: left; /* Align text to the left */
  width: 100%; /* Ensure alignment respects the form's width */
  max-width: 400px; /* Match the form's max width */
}

/* Form styles */
form {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Input styles */
.input-text {
  padding: 12px;
  font-size: 16px;
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #fff;
  color: #333;
  box-sizing: border-box;
  transition:
    border-color 0.3s,
    box-shadow 0.3s;
}

.input-text:focus {
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

/* Button styles */
.signup-button {
  padding: 12px;
  font-size: 16px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.signup-button:hover {
  background-color: #0056b3;
}

.signup-button:active {
  background-color: #003f7f;
}

/* Signup link styles */
.login-link {
  margin-top: 30px; /* Increased spacing */
  font-size: 14px;
  color: #007bff;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.3s;
  text-align: center; /* Center the link */
}

.login-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

.error-message {
  color: red;
  font-size: 14px;
  margin-top: 20px;
  text-align: center;
}
