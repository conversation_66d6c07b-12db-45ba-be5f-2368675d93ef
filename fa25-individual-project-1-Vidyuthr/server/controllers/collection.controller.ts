import express, { Response } from 'express';
import {
  createCollection,
  deleteCollection,
  getCollectionById,
  getCollectionsByUsername,
  addQuestionToCollection as toggleSaveQuestionToCollection,
} from '../services/collection.service';
import {
  CollectionRequest,
  CreateCollectionRequest,
  FakeSOSocket,
  GetCollectionsByUserIdRequest,
  PopulatedDatabaseCollection,
  SaveQuestionRequest,
} from '../types/types';
import { populateDocument } from '../utils/database.util';

const collectionController = (socket: FakeSOSocket) => {
  const router = express.Router();

  /**
   * Checks if the provided answer request contains the required fields.
   *
   * @param req The request object containing the answer data.
   *
   * @returns `true` if the request is valid, otherwise `false`.
   */
  const isRequestValid = (req: CreateCollectionRequest): boolean =>
    !!req.body.name && !!req.body.description && !!req.body.questions && !!req.body.username;

  const isCollectionRequestValid = (req: CollectionRequest): boolean =>
    !!req.params.collectionId && !!req.query.username;

  /**
   * Task 1.5.1 - 1.5 Points
   * Create an endpoint function for adding new collection in the database.
   * Make correct use of error handeling, validation checks and status codes.
   * Make sure to use socket logic and mongoose functions accurately.
   * @param req The request object containing the collection data.
   * @param res The response object.
   * @returns `void`.
   */
  const createCollectionRoute = async (
    req: CreateCollectionRequest,
    res: Response,
  ): Promise<void> => {
    // - Write your code here -

    try {
      if (!isRequestValid) {
        res.status(400).send('Request body is not valid.');
      }
      const { name, description, questions, username, isPrivate } = req.body;

      const createdCollection = await createCollection({
        name,
        description,
        questions,
        username,
        isPrivate,
      });

      if ('error' in createdCollection) {
        throw new Error(createdCollection.error as string);
      }
      const populatedCreatedCollection = (await populateDocument(
        createdCollection._id.toString(),
        'collection',
      )) as PopulatedDatabaseCollection;

      socket.emit('collectionUpdate', {
        type: 'created',
        collection: populatedCreatedCollection,
      });

      res.status(200).json(populatedCreatedCollection);
    } catch (err: unknown) {
      res.status(500).send(`Error when creating collection: ${(err as Error).message}`);
    }
  };

  /**
   * Task 1.5.2 - 1.5 Points
   * Endpoint for Deleting a collection from the database.
   * Make correct use of error handeling, validation checks and status codes.
   * Make sure to use socket logic and mongoose functions accurately.
   * @param req The request object containing the collection id.
   * @param res The response object.
   *
   * @returns `void`.
   */
  const deleteCollectionRoute = async (req: CollectionRequest, res: Response): Promise<void> => {
    try {
      if (!isCollectionRequestValid(req)) {
        res.status(400).send('Invalid collection body');
        return;
      }

      const { collectionId } = req.params;
      const { username } = req.query;

      const deletedCollection = await deleteCollection(collectionId, username);

      if ('error' in deletedCollection) {
        throw new Error(deletedCollection.error as string);
      }

      const populatedDeletedCollection = (await populateDocument(
        deletedCollection._id.toString(),
        'collection',
      )) as PopulatedDatabaseCollection;

      socket.emit('collectionUpdate', {
        type: 'deleted',
        collection: populatedDeletedCollection,
      });

      res.status(200).json(populatedDeletedCollection);
    } catch (err: unknown) {
      res.status(500).send(`Error when deleting collection: ${(err as Error).message}`);
    }
  };

  /**
   * Task 1.5.3 - 1.5 Points
   * Endpoint function to add a question to a collection.
   * Make correct use of error handeling, validation checks and status codes.
   * Make sure to use socket logic and mongoose functions accurately.
   * @param req The request object containing the collection id and question id.
   * @param res The response object.
   *
   * @returns `void`.
   */
  const toggleSaveQuestionRoute = async (
    req: SaveQuestionRequest,
    res: Response,
  ): Promise<void> => {
    // - Write your code here -

    try {
      if (!!req.body.collectionId && !!req.body.questionId && !!req.body.username) {
        res.status(400).send('Request body is not valid.');
      }

      const { collectionId, questionId, username } = req.body;

      const updatedCollection = await toggleSaveQuestionToCollection(
        collectionId,
        questionId,
        username,
      );

      if ('error' in updatedCollection) {
        throw new Error(updatedCollection.error as string);
      }

      const populatedCollection = await populateDocument(
        updatedCollection._id.toString(),
        'collection',
      );

      if ('error' in populatedCollection) {
        throw new Error(populatedCollection.error as string);
      }
    } catch (err: unknown) {
      throw err as Error;
    }
  };

  /**
   * Task 1.5.4 - 1.5 Points
   * Endpoint function to get all collections for a user using their username.
   * Make correct use of error handeling, validation checks and status codes.
   * Make sure to use socket logic and mongoose functions accurately.
   * @param req The request object containing the usernames.
   * @param res The response object containing the collections.
   *
   * @returns `void`.
   */
  const getCollectionsByUsernameRoute = async (
    req: GetCollectionsByUserIdRequest,
    res: Response,
  ): Promise<void> => {
    if (!req.query.currentUsername || !req.params.username) {
      res.status(400).send('Invalid collection body');
      return;
    }

    const { username: usernameToView } = req.params;
    const { currentUsername } = req.query;

    try {
      // - Write your code here -
      const retrievedCollections = await getCollectionsByUsername(
        usernameToView,
        currentUsername as string,
      );

      if ('error' in retrievedCollections) {
        throw new Error(retrievedCollections.error);
      }

      res.status(200).json(retrievedCollections);
    } catch (err: unknown) {
      res.status(500).send(`Error when getting collections by username: ${(err as Error).message}`);
    }
  };

  /**
   * Task 1.5.5 - 1.5 Points
   * Endpoint function to get a collection by its ID.
   * Make correct use of error handeling, validation checks and status codes.
   * Make sure to use socket logic and mongoose functions accurately.
   * @param req The request object containing the collection id.
   * @param res The response object.
   *
   * @returns `void`.
   */
  const getCollectionByIdRoute = async (req: CollectionRequest, res: Response): Promise<void> => {
    // -Write your code here -
    if (!isCollectionRequestValid(req)) {
      res.status(400).send('Invalid collection param');
      return;
    }

    try {
      const { collectionId } = req.params;
      const { username } = req.query;

      const retrievedCollection = await getCollectionById(collectionId, username as string);

      if ('error' in retrievedCollection) {
        throw new Error(retrievedCollection.error);
      }
      const populatedRetrievedCollection = (await populateDocument(
        retrievedCollection._id.toString(),
        'collection',
      )) as PopulatedDatabaseCollection;

      if ('error' in populatedRetrievedCollection) {
        throw new Error(populatedRetrievedCollection.error as string);
      }

      res.status(200).json(populatedRetrievedCollection);
    } catch (err: unknown) {
      res.status(500).send(`Error when getting collection by id: ${(err as Error).message}`);
    }
  };

  /*
   * Task 1.6 - 2.5 Points
   * Define the routes for the collection controller functions.
   * Use `router` to define the routes.
   * Make sure to use appropriate HTTP methods (GET, POST, DELETE, PATCH).
   */
  router.post('/create', createCollectionRoute);
  router.patch('/toggleSaveQuestion', toggleSaveQuestionRoute);
  router.get('/:collectionId', getCollectionByIdRoute);
  router.get('/:username', getCollectionsByUsernameRoute);
  router.delete('/delete/:collectionId', deleteCollectionRoute);

  return router;
};

export default collectionController;
