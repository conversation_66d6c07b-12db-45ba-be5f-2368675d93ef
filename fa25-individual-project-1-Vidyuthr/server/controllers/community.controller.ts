import express, { Response } from 'express';
import {
  createCommunity,
  deleteCommunity,
  getAllCommunities,
  getCommunity,
  toggleCommunityMembership,
} from '../services/community.service';
import {
  CommunityIdRequest,
  CreateCommunityRequest,
  DeleteCommunityRequest,
  FakeSOSocket,
  ToggleMembershipRequest,
} from '../types/types';

/**
 * This controller handles community-related routes.
 * @param socket The socket instance to emit events.
 * @returns {express.Router} The router object containing the community routes.
 * @throws {Error} Throws an error if the community operations fail.
 */
const communityController = (socket: FakeSOSocket) => {
  const router = express.Router();

  /**
   * Validates required fields for creating a community.
   *
   * @param req - The incoming request object containing the community creation data
   * @returns {boolean} - True if all required fields are present, false otherwise
   */
  const isCreateCommunityRequestValid = (req: CreateCommunityRequest): boolean => {
    const { name, description, admin } = req.body;
    return !!name && !!description && !!admin;
  };

  /**
   * Validates required fields for toggling membership.
   *
   * @param req - The request object containing membership toggle information
   * @returns {boolean} - True if all required fields are present, false otherwise
   */
  const isToggleMembershipRequestValid = (req: ToggleMembershipRequest): boolean => {
    const { communityId, username } = req.body;
    return !!communityId && !!username;
  };

  /**
   * Validates required fields for deleting a community.
   *
   * @param req - The request object containing community deletion information
   * @returns {boolean} - True if all required fields are present, false otherwise
   */
  const isDeleteCommunityRequestValid = (req: DeleteCommunityRequest): boolean => {
    return (
      !!req.params &&
      !!req.params.communityId &&
      !!req.body &&
      typeof req.body.username === 'string' &&
      req.body.username.trim() !== ''
    );
  };

  /**
   * Task 2.5.1 - 1.5 Points
   * Write a route handler for retrieving a community by its ID.
   *
   * @param req - The request object containing the communityId parameter
   * @param res - The response object used to send back the result
   * @returns {Promise<void>} - A promise that resolves when the response has been sent
   */
  const getCommunityRoute = async (req: CommunityIdRequest, res: Response): Promise<void> => {
    try {
      if (!req.params.communityId) {
        res.status(400).send('Invalid params.');
        return;
      }
      const { communityId } = req.params;
      const retrievedCommunity = await getCommunity(communityId);
      if ('error' in retrievedCommunity) {
        res.status(500).send(retrievedCommunity.error);
        return;
      }
      res.status(200).json(retrievedCommunity);
    } catch (error) {
      res.status(500).send(`Error when fetching community: ${(error as Error).message}`);
    }
  };

  /**
   * Task 2.5.2 - 1.5 Points
   * Write a route handler for retrieving all communities.
   *
   * @param _req - The express request object (unused, hence the underscore prefix)
   * @param res - The response object used to send back the result
   * @returns {Promise<void>} - A promise that resolves when the response has been sent
   */
  const getAllCommunitiesRoute = async (_req: express.Request, res: Response): Promise<void> => {
    try {
      const retrievedCommunities = await getAllCommunities();

      if ('error' in retrievedCommunities) {
        res.status(500).send(retrievedCommunities.error);
        return;
      }

      res.status(200).json(retrievedCommunities);
    } catch (error) {
      res.status(500).send(`Error when fetching communities: ${(error as Error).message}`);
    }
  };

  /**
   * Task 2.5.3 - 1.5 Points
   * Write a route handler for toggling a user's membership status in a community.
   * Make correct use of error handling, validation checks and status codes, related to user permissions.
   *
   * @param req - The request object containing communityId and username
   * @param res - The response object used to send back the result
   * @returns {Promise<void>} - A promise that resolves when the response has been sent
   */
  const toggleMembershipRoute = async (
    req: ToggleMembershipRequest,
    res: Response,
  ): Promise<void> => {
    try {
      if (!isToggleMembershipRequestValid(req)) {
        res.status(400).send('Missing required fields: communityId and username');
        return;
      }

      const { communityId, username } = req.body;
      const toggledCommunity = await toggleCommunityMembership(communityId, username);

      if ('error' in toggledCommunity) {
        if (toggledCommunity.error === 'Community not found') {
          res.status(404).send(toggledCommunity.error);
        } else if (toggledCommunity.error === 'Admin cannot leave the community') {
          res.status(403).send(toggledCommunity.error);
        } else {
          res.status(500).send(toggledCommunity.error);
        }
        return;
      }
      socket.emit('communityUpdate', {
        community: toggledCommunity,
        type: 'updated',
      });
      res.status(200).json(toggledCommunity);
    } catch (err: unknown) {
      res.status(500).send(`Error when toggling membership: ${(err as Error).message}`);
    }
  };

  /**
   * Task 2.5.4 - 1.5 Points
   * Create a function to handle community creation requests.
   *
   * @param req - The request object containing community details (name, description, admin, etc.)
   * @param res - The response object used to send back the result
   * @returns {Promise<void>} - A promise that resolves when the response has been sent
   */
  const createCommunityRoute = async (
    req: CreateCommunityRequest,
    res: Response,
  ): Promise<void> => {
    try {
      if (!isCreateCommunityRequestValid(req)) {
        res.status(400).send('Missing required fields: name, description, and admin');
        return;
      }

      const { name, description, admin, visibility = 'public', participants = [] } = req.body;
      const communityData = {
        name,
        description,
        admin,
        visibility,
        participants,
      };

      const createdCommunity = await createCommunity(communityData);

      if ('error' in createdCommunity) {
        res.status(500).send(createdCommunity.error);
        return;
      }
      socket.emit('communityUpdate', {
        community: createdCommunity,
        type: 'created',
      });
      res.status(201).json(createdCommunity);
    } catch (error) {
      res.status(500).send(`Error when creating community: ${(error as Error).message}`);
    }
  };

  /**
   * Task 2.5.5 - 1.5 Points
   * Create a function to handle community deletion requests.
   * Use appropriate validation and error handling.
   *
   * @param req - The request object containing communityId and username
   * @param res - The response object used to send back the result
   * @returns {Promise<void>} - A promise that resolves when the response has been sent
   */
  const deleteCommunityRoute = async (
    req: DeleteCommunityRequest,
    res: Response,
  ): Promise<void> => {
    try {
      if (!isDeleteCommunityRequestValid(req)) {
        res.status(400).send('Missing required fields: communityId and username');
        return;
      }

      const { communityId } = req.params;
      const { username } = req.body;
      const deletedCommunity = await deleteCommunity(communityId, username);

      if ('error' in deletedCommunity) {
        if (deletedCommunity.error === 'Community not found') {
          res.status(404).send(deletedCommunity.error);
        } else if (deletedCommunity.error === 'Only the admin can delete this community') {
          res.status(403).send(deletedCommunity.error);
        } else {
          res.status(500).send(deletedCommunity.error);
        }
        return;
      }
      socket.emit('communityUpdate', {
        community: deletedCommunity,
        type: 'deleted',
      });
      res.status(200).json(deletedCommunity);
    } catch (error) {
      res.status(500).send(`Error when deleting community: ${(error as Error).message}`);
    }
  };

  /**
   * Task 2.6 - 2.5 Points
   * Write Route Handlers for Community Operations
   * Make sure to use appropriate HTTP status codes and error handling.
   */

  router.get('/getCommunity/:communityId', getCommunityRoute);
  router.get('/getAllCommunities', getAllCommunitiesRoute);
  router.post('/toggleMembership', toggleMembershipRoute);
  router.post('/create', createCommunityRoute);
  router.delete('/delete/:communityId', deleteCommunityRoute);

  return router;
};

export default communityController;
