import CommunityModel from '../models/community.model';
import { Community, CommunityResponse, DatabaseCommunity } from '../types/types';

/**
 * Task 2.4.1 - 3 points
 * Write a function to retrieve a community by its ID.
 * @param communityId - The ID of the community to retrieve
 * @returns A Promise resolving to the community document or an error object
 */
export const getCommunity = async (communityId: string): Promise<CommunityResponse> => {
  // - Write your code here -
  const community = await CommunityModel.findById(communityId);
  if (!community) {
    throw new Error('Community not found');
  }

  return community;
};

/**
 * Task 2.4.2 - 3 points
 * Write a function to retrieve all communities in the database.
 * @returns A Promise resolving to an array of community documents or an error object
 */
export const getAllCommunities = async (): Promise<DatabaseCommunity[] | { error: string }> => {
  // - Write your code here -
  const communities = await CommunityModel.find();
  if (!communities) {
    throw new Error('Community not found');
  }
  return communities;
};

/**
 * Task 2.4.3 - 5 points
 * Write a function to toggle a user's membership status in a community.
 * If the user is already a participant, they will be removed.
 * If the user is not a participant, they will be added.
 * @param communityId - The ID of the community to update
 * @param username - The username of the user whose membership to toggle
 * @returns A Promise resolving to the updated community document or an error object
 */
export const toggleCommunityMembership = async (
  communityId: string,
  username: string,
): Promise<CommunityResponse> => {
  // - Write your code here -
  const community = await CommunityModel.findById(communityId);

  if (!community) {
    return { error: 'Community not found' };
  }

  const participantIndex = community.participants.indexOf(username);

  if (participantIndex !== -1) {
    // User is already a participant, remove them
    // But don't allow admin to remove themselves
    if (community.admin === username) {
      return { error: 'Admin cannot leave the community' };
    }
    community.participants.splice(participantIndex, 1);
  } else {
    // User is not a participant, add them
    community.participants.push(username);
  }

  const updatedCommunity = await community.save();
  return updatedCommunity;
};

/**
 * Task 2.4.4 - 5 points
 * Write a function to create a new community.
 * The participants list must include the admin user.
 * @param communityData - Object containing community details including name, description, visibility, admin, and participants
 * @returns A Promise resolving to the newly created community document or an error object
 */
export const createCommunity = async (communityData: Community): Promise<CommunityResponse> => {
  // - Write your code here -
  const existingCommunity = await CommunityModel.findOne(communityData);
  if (existingCommunity) {
    throw new Error('This community already exists.');
  }
  const newCommunity = new CommunityModel(communityData);
  const savedCommunity = await newCommunity.save();

  return savedCommunity;
};

/**
 * Task 2.4.5 - 4 points
 * Write a function to delete a community by its ID.
 * The user must be the admin of the community to delete it.
 * Handle errors appropriately.
 * @param communityId - The ID of the community to delete
 * @param username - The username of the user requesting deletion
 * @returns A Promise resolving to a success object or an error object
 */
export const deleteCommunity = async (
  communityId: string,
  username: string,
): Promise<CommunityResponse> => {
  // - Write your code here -
  const deletedCommunity = await CommunityModel.findOneAndDelete({
    _id: communityId,
    admin: username,
  });
  if (!deletedCommunity) {
    throw new Error('This community does not exist or cannot delete this community.');
  }

  return deletedCommunity;
};
