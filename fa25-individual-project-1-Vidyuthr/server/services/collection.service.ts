import mongoose from 'mongoose';
import CollectionModel from '../models/collection.model';
import { Collection, CollectionResponse, DatabaseCollection } from '../types/types';

/**
 * Task 1.4.1 - 4 points
 * Create a new collection in the database.
 * Make correct use of error handeling, and mongoose functions.
 * @param collection - The collection data to create
 * @returns A Promise resolving to the created collection or an error object
 */
export const createCollection = async (collection: Collection): Promise<CollectionResponse> => {
  const existingCollection = await CollectionModel.findOne({
    name: collection.name,
    username: collection.username,
    isPrivate: collection.isPrivate,
  });

  if (existingCollection) {
    throw new Error('This collection already exists.');
  }

  const newCollection = new CollectionModel(collection);
  const savedCollection = await newCollection.save();

  return savedCollection;
};

/**
 * Task 1.4.2 - 4 points
 * Delete a collection from the database.
 * Make correct use of error handeling, and mongoose functions.
 * @param id - The ID of the collection to delete
 * @param username - The username of the user requesting deletion
 * @returns A Promise resolving to a success object or an error object
 */
export const deleteCollection = async (
  id: string,
  username: string,
): Promise<CollectionResponse> => {
  const deletedCollection = await CollectionModel.findOneAndDelete({
    _id: id,
    username: username,
  });
  if (!deletedCollection) {
    throw new Error('This collection does not exist.');
  }

  return deletedCollection;
};

/**
 * Task 1.4.3 - 4 points
 * Get all the collections for a user using their username.
 * @param usernameToView - The username of the user whose collections to view
 * @param currentUsername - The username of the user requesting the view
 * @returns A Promise resolving to an array of collections or an error object
 */
export const getCollectionsByUsername = async (
  usernameToView: string,
  currentUsername: string,
): Promise<DatabaseCollection[] | { error: string }> => {
  try {
    const filter: { username: string; isPrivate?: boolean } = {
      username: usernameToView,
    };

    // If viewing someone else's collections, exclude private ones
    if (usernameToView !== currentUsername) {
      filter.isPrivate = false;
    }

    const collections = await CollectionModel.find(filter);
    return collections;
  } catch (error) {
    return { error: 'Failed to retrieve collections' };
  }
};

/**
 * Task 1.4.4 - 4 points
 * Get a collection by id.
 * @param id - The ID of the collection to retrieve
 * @param username - The username of the user requesting the collection
 * @returns A Promise resolving to the collection or an error object
 */
export const getCollectionById = async (
  id: string,
  username: string,
): Promise<CollectionResponse> => {
  const collection = await CollectionModel.findById(id);

  if (!collection) {
    throw new Error('Collection not found');
  }

  // Check if private collection is being accessed by non-owner
  if (collection.isPrivate && collection.username !== username) {
    throw new Error('Access denied to private collection');
  }

  return collection;
};

/**
 * Task 1.4.5 - 4 points
 * Add a question to a collection if it is not already present
 * else remove it from the collection.
 * @param collectionId - The ID of the collection to update
 * @param questionId - The ID of the question to add or remove
 * @param username - The username of the user requesting the update
 */
export const addQuestionToCollection = async (
  collectionId: string,
  questionId: string,
  username: string,
): Promise<CollectionResponse> => {
  const collection = await CollectionModel.findOne({
    _id: collectionId,
    username: username,
  });

  if (!collection) {
    throw new Error('Collection not found or user not authorized');
  }

  const questionObjectId = new mongoose.Types.ObjectId(questionId);

  // Check if question already exists in collection
  const questionIndex = collection.questions.findIndex(q => q.toString() === questionId);

  if (questionIndex !== -1) {
    // Remove question if it exists (toggle off)
    collection.questions.splice(questionIndex, 1);
  } else {
    // Add question if it doesn't exist (toggle on)
    collection.questions.push(questionObjectId);
  }

  const updatedCollection = await collection.save();
  return updatedCollection;
};
