import mongoose from 'mongoose';
import CollectionModel from '../../models/collection.model';
import {
  addQuestionToCollection,
  createCollection,
  deleteCollection,
  getCollectionById,
  getCollectionsByUsername,
} from '../../services/collection.service';
import { Collection, DatabaseCollection } from '../../types/types';

jest.mock('../../models/collection.model');

describe('Collection Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Mock question IDs for testing
  const mockQuestionId1 = new mongoose.Types.ObjectId('65e9b58910afe6e94fc6e6aa');

  const mockCollection: DatabaseCollection = {
    _id: new mongoose.Types.ObjectId('65e9b58910afe6e94fc6e6dd'),
    name: 'Test Collection',
    description: 'Test Description',
    username: 'test_user',
    questions: [mockQuestionId1],
    isPrivate: false,
  };

  const mockCollectionInput: Collection = {
    name: 'New Collection',
    description: 'New Description',
    username: 'new_user',
    questions: [],
    isPrivate: true,
  };

  describe('createCollection', () => {
    test('should create a new collection successfully', async () => {
      jest.spyOn(CollectionModel, 'findOne').mockResolvedValue(null);

      const mockSave = jest.fn().mockResolvedValue(mockCollection);
      jest.spyOn(CollectionModel.prototype, 'save').mockImplementation(mockSave);

      const result = await createCollection(mockCollectionInput);

      expect(CollectionModel.findOne).toHaveBeenCalledWith({
        name: mockCollectionInput.name,
        username: mockCollectionInput.username,
        isPrivate: mockCollectionInput.isPrivate,
      });
      expect(mockSave).toHaveBeenCalled();
      expect(result).toEqual(mockCollection);
    });

    test('should return error when creation fails', async () => {

      jest.spyOn(CollectionModel, 'findOne').mockResolvedValue(mockCollection);

      await expect(createCollection(mockCollectionInput)).rejects.toThrow(
        'This collection already exists.',
      );
    });

    test('should return error when database throws error', async () => {
      // Mock findOne to throw an error
      jest.spyOn(CollectionModel, 'findOne').mockRejectedValue(new Error('Database error'));

      await expect(createCollection(mockCollectionInput)).rejects.toThrow('Database error');
    });
  });

  describe('deleteCollection', () => {
    test('should delete collection when it exists and belongs to user', async () => {
      // Mock findOneAndDelete to return the deleted collection
      jest.spyOn(CollectionModel, 'findOneAndDelete').mockResolvedValue(mockCollection);

      const result = await deleteCollection(mockCollection._id.toString(), mockCollection.username);

      expect(CollectionModel.findOneAndDelete).toHaveBeenCalledWith({
        _id: mockCollection._id.toString(),
        username: mockCollection.username,
      });
      expect(result).toEqual(mockCollection);
    });

    test('should throw error when collection not found', async () => {
      // Mock findOneAndDelete to return null
      jest.spyOn(CollectionModel, 'findOneAndDelete').mockResolvedValue(null);

      await expect(deleteCollection('nonexistent_id', 'test_user')).rejects.toThrow(
        'This collection does not exist.',
      );
    });

    test('should throw error when deletion fails', async () => {
      // Mock findOneAndDelete to throw an error
      jest
        .spyOn(CollectionModel, 'findOneAndDelete')
        .mockRejectedValue(new Error('Database error'));

      await expect(
        deleteCollection(mockCollection._id.toString(), mockCollection.username),
      ).rejects.toThrow('Database error');
    });

    test('should not delete collection belonging to another user', async () => {
      // Mock findOneAndDelete to return null (no match for different user)
      jest.spyOn(CollectionModel, 'findOneAndDelete').mockResolvedValue(null);

      await expect(
        deleteCollection(mockCollection._id.toString(), 'different_user'),
      ).rejects.toThrow('This collection does not exist.');
    });
  });

  describe('getCollectionsByUsername', () => {
    test('should return all collections for owner', async () => {
      // Owner can see both public and private collections
      const mockPrivateCollection = { ...mockCollection, isPrivate: true };
      const mockCollections = [mockCollection, mockPrivateCollection];

      jest.spyOn(CollectionModel, 'find').mockResolvedValue(mockCollections);

      const result = await getCollectionsByUsername('test_user', 'test_user');

      expect(CollectionModel.find).toHaveBeenCalledWith({
        username: 'test_user',
      });
      expect(result).toEqual(mockCollections);
    });

    test('should filter out private collections for non-owner', async () => {
      // Non-owner should only see public collections
      jest.spyOn(CollectionModel, 'find').mockResolvedValue([mockCollection]);

      const result = await getCollectionsByUsername('test_user', 'different_user');

      expect(CollectionModel.find).toHaveBeenCalledWith({
        username: 'test_user',
        isPrivate: false,
      });
      expect(result).toEqual([mockCollection]);
    });

    test('should return empty array when no collections found', async () => {
      jest.spyOn(CollectionModel, 'find').mockResolvedValue([]);

      const result = await getCollectionsByUsername('test_user', 'test_user');

      expect(result).toEqual([]);
    });

    test('should return error when find returns null', async () => {
      // This test might not be applicable since find() returns an array, not null
      // But we can test for database errors instead
      jest.spyOn(CollectionModel, 'find').mockRejectedValue(new Error('Database error'));

      const result = await getCollectionsByUsername('test_user', 'test_user');

      expect(result).toEqual({ error: 'Failed to retrieve collections' });
    });

    test('should return error when database throws error', async () => {
      jest
        .spyOn(CollectionModel, 'find')
        .mockRejectedValue(new Error('Database connection failed'));

      const result = await getCollectionsByUsername('test_user', 'test_user');

      expect(result).toEqual({ error: 'Failed to retrieve collections' });
    });
  });

  describe('getCollectionById', () => {
    test('should return collection when it is public', async () => {
      jest.spyOn(CollectionModel, 'findById').mockResolvedValue(mockCollection);

      const result = await getCollectionById(mockCollection._id.toString(), 'any_user');

      expect(CollectionModel.findById).toHaveBeenCalledWith(mockCollection._id.toString());
      expect(result).toEqual(mockCollection);
    });

    test('should return collection when user is owner and collection is private', async () => {
      const mockPrivateCollection = { ...mockCollection, isPrivate: true };
      jest.spyOn(CollectionModel, 'findById').mockResolvedValue(mockPrivateCollection);

      const result = await getCollectionById(
        mockCollection._id.toString(),
        mockCollection.username,
      );

      expect(result).toEqual(mockPrivateCollection);
    });

    test('should return error when collection is private and user is not owner', async () => {
      const mockPrivateCollection = { ...mockCollection, isPrivate: true };
      jest.spyOn(CollectionModel, 'findById').mockResolvedValue(mockPrivateCollection);

      await expect(
        getCollectionById(mockCollection._id.toString(), 'different_user'),
      ).rejects.toThrow('Access denied to private collection');
    });

    test('should return error when collection not found', async () => {
      jest.spyOn(CollectionModel, 'findById').mockResolvedValue(null);

      await expect(getCollectionById('nonexistent_id', 'any_user')).rejects.toThrow(
        'Collection not found',
      );
    });

    test('should return error when database throws error', async () => {
      jest.spyOn(CollectionModel, 'findById').mockRejectedValue(new Error('Database error'));

      await expect(getCollectionById(mockCollection._id.toString(), 'any_user')).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('addQuestionToCollection', () => {
    test('should add question when it is not in collection', async () => {
      // Collection starts with empty questions array
      const mockEmptyCollection = { ...mockCollection, questions: [] };
      const mockUpdatedCollection = { ...mockCollection, questions: [mockQuestionId1] };

      jest.spyOn(CollectionModel, 'findOne').mockResolvedValue({
        ...mockEmptyCollection,
        save: jest.fn().mockResolvedValue(mockUpdatedCollection),
      });

      const result = await addQuestionToCollection(
        mockCollection._id.toString(),
        mockQuestionId1.toString(),
        mockCollection.username,
      );

      expect(CollectionModel.findOne).toHaveBeenCalledWith({
        _id: mockCollection._id.toString(),
        username: mockCollection.username,
      });
      expect(result).toEqual(mockUpdatedCollection);
    });

    test('should remove question when it is already in collection', async () => {
      // Mock collection with existing question
      const mockUpdatedCollection = { ...mockCollection, questions: [] };

      jest.spyOn(CollectionModel, 'findOne').mockResolvedValue({
        ...mockCollection,
        save: jest.fn().mockResolvedValue(mockUpdatedCollection),
      });

      const result = await addQuestionToCollection(
        mockCollection._id.toString(),
        mockQuestionId1.toString(),
        mockCollection.username,
      );

      expect(result).toEqual(mockUpdatedCollection);
    });

    test('should return error when collection not found', async () => {
      jest.spyOn(CollectionModel, 'findOne').mockResolvedValue(null);

      await expect(
        addQuestionToCollection('nonexistent_id', mockQuestionId1.toString(), 'test_user'),
      ).rejects.toThrow('Collection not found or user not authorized');
    });

    test('should return error when user does not own collection', async () => {
      jest.spyOn(CollectionModel, 'findOne').mockResolvedValue(null);

      await expect(
        addQuestionToCollection(
          mockCollection._id.toString(),
          mockQuestionId1.toString(),
          'different_user',
        ),
      ).rejects.toThrow('Collection not found or user not authorized');
    });

    test('should return error when update fails', async () => {
      jest.spyOn(CollectionModel, 'findOne').mockResolvedValue({
        ...mockCollection,
        save: jest.fn().mockRejectedValue(new Error('Save failed')),
      });

      await expect(
        addQuestionToCollection(
          mockCollection._id.toString(),
          mockQuestionId1.toString(),
          mockCollection.username,
        ),
      ).rejects.toThrow('Save failed');
    });

    test('should return error when database throws error', async () => {
      jest.spyOn(CollectionModel, 'findOne').mockRejectedValue(new Error('Database error'));

      await expect(
        addQuestionToCollection(
          mockCollection._id.toString(),
          mockQuestionId1.toString(),
          mockCollection.username,
        ),
      ).rejects.toThrow('Database error');
    });
  });
});
