import { Schema } from 'mongoose';
/**
 * Mongoose schema for the Collection collection.
 *
 * This schema defines the structure for storing collections in the database.
 * Each collection includes the following fields:
 * - `name`: The name of the collection.
 * - `description`: The description of the collection.
 * - `questions`: The questions that have been added to the collection.
 * - `username`: The user that created the collection.
 * - `isPrivate`: Whether the collection is private.
 */
const collectionSchema: Schema = new Schema(
  {
    /*
     * Task 1.1 - 4 points
     * The collection schema must accommodate the following fields:
     * name, description, questions, userId, isPrivate.
     * Make sure to include necessary validations, default values, and types.
     */
    // - Write your code here -
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    questions: {
      type: [Schema.Types.ObjectId],
      ref: 'Question',
      default: [],
    },
    username: {
      type: String,
      required: true,
    },
    isPrivate: {
      type: Boolean,
      required: true,
      default: true,
    },
  },
  { collection: 'Collection' },
);

export default collectionSchema;
