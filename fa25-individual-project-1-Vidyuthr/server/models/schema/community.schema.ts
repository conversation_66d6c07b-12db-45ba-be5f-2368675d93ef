import { Schema } from 'mongoose';

/**
 * Mongoose schema for the Community collection.
 *
 * - `participants`: an array of ObjectIds referencing the User collection.
 * - `questions`: an array of ObjectIds referencing the Question collection.
 * - Timestamps store `createdAt` & `updatedAt`.
 * - `name`: Name of the community.
 * - `description`: description of the community.
 * - `visibility`: enum [PUBLIC, PRIVATE].
 */

const communitySchema = new Schema(
  {
    /*
     * Task 2.1 - 4 points
     * The community schema must accommodate the following fields:
     * name, description, participants, questions, visibility.
     * Make sure to include necessary validations, default values, and types.
     * May contain enums.
     */
    name: {
      type: String,
      required: true,
      unique: true,
    },
    description: {
      type: String,
    },
    participants: {
      type: [Schema.Types.ObjectId],
      ref: 'User',
      default: [],
    },
    visibility: {
      type: String,
      enum: ['public', 'private'],
      default: 'public',
    },
    admin: {
      type: String,
      required: true,
    },
  },
  {
    collection: 'Community',
    timestamps: true,
  },
);

export default communitySchema;
